import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { BaseStepComponent, StepComponent, BaseStepProps, StepData, StageData } from './BaseStepComponent';
import { cn } from '@/lib/utils';

export interface StepContentProps {
  stepData: StepData;
  stageData: StageData;
  isActive: boolean;
  isCompleted: boolean;
  onComplete: (stepId: string) => void;
}

export interface StepComponentWrapperProps extends BaseStepProps {
  renderStepContent?: (props: StepContentProps) => React.ReactNode;
  useAccordion?: boolean;
  defaultExpandedStep?: string;
  allowMultipleExpanded?: boolean;
}

export const StepComponentWrapper: React.FC<StepComponentWrapperProps> = ({
  stageData,
  currentStep,
  stepStatus,
  onStepComplete,
  onNextStep,
  onPrevStep,
  className,
  renderStepContent,
  useAccordion = true,
  defaultExpandedStep,
  allowMultipleExpanded = false,
  children,
}) => {
  const currentStepData = stageData.steps[currentStep];

  // Render individual step content
  const renderStep = (stepData: StepData, stepIndex: number) => {
    const isActive = stepIndex === currentStep;
    const isCompleted = stepStatus[stepData.id] || false;

    const stepContentProps: StepContentProps = {
      stepData,
      stageData,
      isActive,
      isCompleted,
      onComplete: onStepComplete,
    };

    // Use custom render function if provided
    if (renderStepContent) {
      return renderStepContent(stepContentProps);
    }

    // Default step rendering
    return (
      <StepComponent
        key={stepData.id}
        stepData={stepData}
        stageData={stageData}
        isActive={isActive}
        isCompleted={isCompleted}
        onComplete={onStepComplete}
      />
    );
  };

  // Render steps with accordion
  const renderAccordionSteps = () => {
    const accordionType = allowMultipleExpanded ? "multiple" : "single";
    const defaultValue = defaultExpandedStep || currentStepData?.id;

    return (
      <Accordion 
        type={accordionType} 
        collapsible 
        defaultValue={defaultValue}
        className="space-y-4"
      >
        {stageData.steps.map((stepData, stepIndex) => {
          const isActive = stepIndex === currentStep;
          const isCompleted = stepStatus[stepData.id] || false;
          const Icon = stepData.icon;

          return (
            <AccordionItem 
              key={stepData.id}
              value={stepData.id} 
              className={cn(
                "border-2 rounded-lg",
                isActive ? "border-primary" : "border-gray-200"
              )}
            >
              <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
                <div className="flex items-center space-x-4">
                  <Icon className={cn(
                    "h-6 w-6",
                    isCompleted ? "text-green-600" : isActive ? "text-primary" : "text-gray-400"
                  )} />
                  <span className="text-lg font-semibold">
                    {stageData.id}.{stepIndex + 1} {stepData.title}
                  </span>
                  <div className={cn(
                    "px-3 py-1 rounded-full text-xs font-medium",
                    isCompleted ? "bg-green-100 text-green-800" :
                    isActive ? "bg-primary/10 text-primary" :
                    "bg-gray-100 text-gray-600"
                  )}>
                    {isCompleted ? "Completed" : isActive ? "Active" : "Pending"}
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-6 pb-6">
                {renderStep(stepData, stepIndex)}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    );
  };

  // Render steps without accordion (simple list)
  const renderSimpleSteps = () => {
    return (
      <div className="space-y-6">
        {stageData.steps.map((stepData, stepIndex) => renderStep(stepData, stepIndex))}
      </div>
    );
  };

  return (
    <BaseStepComponent
      stageData={stageData}
      currentStep={currentStep}
      stepStatus={stepStatus}
      onStepComplete={onStepComplete}
      onNextStep={onNextStep}
      onPrevStep={onPrevStep}
      className={className}
    >
      {children || (useAccordion ? renderAccordionSteps() : renderSimpleSteps())}
    </BaseStepComponent>
  );
};

// Higher-order component for creating step components with consistent API
export const withStepWrapper = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    useAccordion?: boolean;
    defaultExpandedStep?: string;
    allowMultipleExpanded?: boolean;
  }
) => {
  const WithStepWrapper: React.FC<P & BaseStepProps> = (props) => {
    return (
      <StepComponentWrapper
        {...props}
        useAccordion={options?.useAccordion}
        defaultExpandedStep={options?.defaultExpandedStep}
        allowMultipleExpanded={options?.allowMultipleExpanded}
      >
        <WrappedComponent {...(props as P)} />
      </StepComponentWrapper>
    );
  };

  WithStepWrapper.displayName = `withStepWrapper(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithStepWrapper;
};

export default StepComponentWrapper;
