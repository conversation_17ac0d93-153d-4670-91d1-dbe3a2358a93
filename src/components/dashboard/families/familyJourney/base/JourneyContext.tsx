import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useJourneyState, JourneyState, JourneyActions, UseJourneyStateReturn } from '@/hooks/useJourneyState';
import { useJourneyProgress, restoreJourneyState } from '@/hooks/useJourneyProgress';
import { stages, journeyConfig } from '@/data/familyJourney/familyJourneyStages';

export interface JourneyContextValue extends UseJourneyStateReturn {
  familyId?: string;
  isLoading: boolean;
  error?: string;
  saveProgress: () => void;
  loadProgress: () => void;
  clearProgress: () => void;
  resetJourney: () => void;
}

const JourneyContext = createContext<JourneyContextValue | undefined>(undefined);

export interface JourneyProviderProps {
  children: ReactNode;
  familyId?: string;
  autoLoadProgress?: boolean;
  autoSaveProgress?: boolean;
}

export const JourneyProvider: React.FC<JourneyProviderProps> = ({
  children,
  familyId,
  autoLoadProgress = true,
  autoSaveProgress = true,
}) => {
  const journeyState = useJourneyState();
  const progressHook = useJourneyProgress();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | undefined>();

  // Load progress on mount
  useEffect(() => {
    if (!autoLoadProgress || !journeyConfig.behavior.persistProgress) {
      return;
    }

    setIsLoading(true);
    try {
      const savedProgress = progressHook.loadProgress(familyId);
      if (savedProgress) {
        const restoredState = restoreJourneyState(savedProgress, journeyState.state);
        
        // Apply restored state
        if (restoredState.currentStage !== undefined) {
          journeyState.actions.setCurrentStage(restoredState.currentStage);
        }
        if (restoredState.currentStep !== undefined) {
          journeyState.actions.setCurrentStep(restoredState.currentStep);
        }
        if (restoredState.expandedStages) {
          journeyState.actions.setExpandedStages(restoredState.expandedStages);
        }
        if (restoredState.stepStatus) {
          journeyState.actions.setStepStatus(restoredState.stepStatus);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load progress');
      console.error('Failed to load journey progress:', err);
    } finally {
      setIsLoading(false);
    }
  }, [familyId, autoLoadProgress]); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-save progress when state changes
  useEffect(() => {
    if (!autoSaveProgress || !journeyConfig.behavior.persistProgress || isLoading) {
      return;
    }

    const timeoutId = setTimeout(() => {
      try {
        progressHook.saveProgress(journeyState.state, familyId);
      } catch (err) {
        console.warn('Failed to auto-save progress:', err);
      }
    }, 1000); // Debounce saves

    return () => clearTimeout(timeoutId);
  }, [
    journeyState.state.currentStage,
    journeyState.state.currentStep,
    journeyState.state.expandedStages,
    journeyState.state.stepStatus,
    familyId,
    autoSaveProgress,
    isLoading,
    progressHook,
    journeyState.state,
  ]);

  // Manual save progress
  const saveProgress = React.useCallback(() => {
    try {
      progressHook.saveProgress(journeyState.state, familyId);
      setError(undefined);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save progress';
      setError(errorMessage);
      console.error('Failed to save journey progress:', err);
    }
  }, [progressHook, journeyState.state, familyId]);

  // Manual load progress
  const loadProgress = React.useCallback(() => {
    setIsLoading(true);
    try {
      const savedProgress = progressHook.loadProgress(familyId);
      if (savedProgress) {
        const restoredState = restoreJourneyState(savedProgress, journeyState.state);
        
        // Apply restored state
        if (restoredState.currentStage !== undefined) {
          journeyState.actions.setCurrentStage(restoredState.currentStage);
        }
        if (restoredState.currentStep !== undefined) {
          journeyState.actions.setCurrentStep(restoredState.currentStep);
        }
        if (restoredState.expandedStages) {
          journeyState.actions.setExpandedStages(restoredState.expandedStages);
        }
        if (restoredState.stepStatus) {
          journeyState.actions.setStepStatus(restoredState.stepStatus);
        }
      }
      setError(undefined);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load progress';
      setError(errorMessage);
      console.error('Failed to load journey progress:', err);
    } finally {
      setIsLoading(false);
    }
  }, [progressHook, familyId, journeyState.actions, journeyState.state]);

  // Clear progress
  const clearProgress = React.useCallback(() => {
    try {
      progressHook.clearProgress(familyId);
      setError(undefined);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear progress';
      setError(errorMessage);
      console.error('Failed to clear journey progress:', err);
    }
  }, [progressHook, familyId]);

  // Reset journey to initial state
  const resetJourney = React.useCallback(() => {
    journeyState.actions.setCurrentStage(1);
    journeyState.actions.setCurrentStep(0);
    journeyState.actions.setExpandedStages(journeyConfig.ui.stages.defaultExpanded);
    journeyState.actions.setSidebarCollapsed(journeyConfig.ui.sidebar.defaultCollapsed);
    journeyState.actions.setMobileSidebarOpen(false);
    
    // Reset step status to initial values
    const initialStatus: { [key: string]: boolean } = {};
    stages.forEach(stage => {
      stage.steps.forEach(step => {
        initialStatus[step.id] = step.defaultStatus;
      });
    });
    journeyState.actions.setStepStatus(initialStatus);
    
    // Clear saved progress
    clearProgress();
  }, [journeyState.actions, clearProgress]);

  const contextValue: JourneyContextValue = {
    ...journeyState,
    familyId,
    isLoading,
    error,
    saveProgress,
    loadProgress,
    clearProgress,
    resetJourney,
  };

  return (
    <JourneyContext.Provider value={contextValue}>
      {children}
    </JourneyContext.Provider>
  );
};

// Hook to use the journey context
export const useJourneyContext = (): JourneyContextValue => {
  const context = useContext(JourneyContext);
  if (context === undefined) {
    throw new Error('useJourneyContext must be used within a JourneyProvider');
  }
  return context;
};

// Hook to use journey state (shorthand)
export const useJourney = () => {
  const context = useJourneyContext();
  return {
    state: context.state,
    actions: context.actions,
    currentStageData: context.currentStageData,
    currentStepData: context.currentStepData,
  };
};

export default JourneyContext;
