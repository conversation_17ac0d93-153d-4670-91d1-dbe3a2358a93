import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

export interface StepData {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: LucideIcon;
  defaultStatus: boolean;
}

export interface StageData {
  id: number;
  title: string;
  description: string;
  icon: LucideIcon;
  componentName: string;
  borderColor: string;
  badgeColor: string;
  steps: StepData[];
}

export interface BaseStepProps {
  stageData: StageData;
  currentStep: number;
  stepStatus: { [key: string]: boolean };
  onStepComplete: (stepId: string) => void;
  onNextStep: () => void;
  onPrevStep: () => void;
  className?: string;
  children?: React.ReactNode;
}

export interface StepComponentProps {
  stepData: StepData;
  stageData: StageData;
  isActive: boolean;
  isCompleted: boolean;
  onComplete: (stepId: string) => void;
  className?: string;
}

export const BaseStepComponent: React.FC<BaseStepProps> = ({
  stageData,
  currentStep,
  stepStatus,
  onStepComplete,
  onNextStep,
  onPrevStep,
  className,
  children,
}) => {
  const currentStepData = stageData.steps[currentStep];
  const isStepCompleted = stepStatus[currentStepData?.id] || false;

  return (
    <div className={cn("space-y-6 lg:space-y-8", className)}>
      {/* Stage Header */}
      <Card className={cn("shadow-lg border-l-8", stageData.borderColor)}>
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className={cn("text-white px-4 py-2 rounded-full text-sm font-bold", stageData.badgeColor)}>
              Step {stageData.id}
            </span>
            <span className="text-2xl font-bold">{stageData.title}</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            {stageData.description}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Step Content */}
      <div className="space-y-4">
        {children}
      </div>

      {/* Navigation Footer */}
      <div className="flex justify-between items-center pt-8">
        <Button
          variant="outline"
          onClick={onPrevStep}
          disabled={stageData.id === 1 && currentStep === 0}
          className="px-6"
        >
          Previous
        </Button>

        <div className="text-center">
          <div className="text-sm text-muted-foreground">
            Step {currentStep + 1} of {stageData.steps.length}
          </div>
          <div className="font-medium text-sm">{stageData.title}</div>
        </div>

        <Button
          onClick={() => onStepComplete(currentStepData?.id)}
          disabled={isStepCompleted}
          className={cn("px-6", stageData.badgeColor)}
        >
          {isStepCompleted ? "Completed" : "Continue"}
        </Button>
      </div>
    </div>
  );
};

// Individual Step Component Base
export const StepComponent: React.FC<StepComponentProps> = ({
  stepData,
  stageData,
  isActive,
  isCompleted,
  onComplete,
  className,
  children,
}) => {
  const Icon = stepData.icon;

  return (
    <Card className={cn(
      "border-2 rounded-lg transition-all duration-200",
      isActive ? "border-primary shadow-md" : "border-gray-200",
      className
    )}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Icon className={cn(
              "h-6 w-6",
              isCompleted ? "text-green-600" : isActive ? "text-primary" : "text-gray-400"
            )} />
            <span className="text-lg font-semibold">{stepData.title}</span>
          </div>
          <Badge 
            className={cn(
              isCompleted ? "bg-green-600 text-white" : 
              isActive ? stageData.badgeColor + " text-white" : 
              "bg-gray-200 text-gray-600"
            )}
          >
            {isCompleted ? "Completed" : isActive ? "Active" : "Pending"}
          </Badge>
        </CardTitle>
        <CardDescription className="text-gray-600">
          {stepData.description}
        </CardDescription>
      </CardHeader>
      
      {(isActive || isCompleted) && (
        <CardContent>
          {children}
          
          {isActive && !isCompleted && (
            <div className="mt-6 flex justify-end">
              <Button
                onClick={() => onComplete(stepData.id)}
                className={cn("px-6", stageData.badgeColor)}
              >
                Complete Step
              </Button>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export default BaseStepComponent;
