import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle } from 'lucide-react';

export default function DepartureStep() {
  return (
    <div className="space-y-8">

      <Card className="border-2 border-gray-300 bg-gray-50 shadow-lg">
        <CardContent className="pt-8 pb-8">
          <div className="text-center py-12">
            <AlertCircle className="h-20 w-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Step Not Available</h3>
            <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
              This step will become available near the end of your au pair's program.
            </p>
            <Badge variant="secondary" className="bg-gray-200 text-gray-700 px-4 py-2 text-lg">
              Pending Previous Steps
            </Badge>
          </div>
        </CardContent>
      </Card>

    </div>
  );
}
