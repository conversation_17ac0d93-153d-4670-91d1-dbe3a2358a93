import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Menu } from 'lucide-react';
import { useJourneyContext } from '../base/JourneyContext';

export interface JourneyHeaderProps {
  className?: string;
}

export const JourneyHeader: React.FC<JourneyHeaderProps> = ({ className }) => {
  const { state, actions, currentStageData, currentStepData } = useJourneyContext();

  return (
    <div className={cn("border-b p-3 lg:p-4 xl:p-6 bg-muted/5 flex-shrink-0", className)}>
      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center gap-2 min-w-0 flex-1">
          {/* Current stage info */}
          <div className="min-w-0 flex-1">
            <h1 className="text-lg lg:text-xl font-semibold truncate">
              {currentStageData.title}
            </h1>
            <p className="text-sm text-muted-foreground truncate">
              {currentStepData.title}
            </p>
          </div>
        </div>

        {/* Mobile Navigation Button - Top Right */}
        <div className="lg:hidden">
          <Button
            variant={state.mobileSidebarOpen ? "secondary" : "outline"}
            size="sm"
            className={cn(
              "h-9 w-9 p-0 transition-all duration-200",
              state.mobileSidebarOpen
                ? "bg-primary/10 border-primary/20 text-primary hover:bg-primary/15"
                : "border-border/60 hover:bg-muted/60 hover:border-border"
            )}
            onClick={state.mobileSidebarOpen ? actions.closeMobileSidebar : actions.openMobileSidebar}
            aria-label={state.mobileSidebarOpen ? "Close navigation sidebar" : "Open navigation sidebar"}
          >
            <Menu className={cn(
              "h-4 w-4 transition-transform duration-200",
              state.mobileSidebarOpen ? "rotate-90" : ""
            )} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default JourneyHeader;
