// Export all journey components
export { default as JourneyHeader } from './JourneyHeader';
export { default as JourneyFooter } from './JourneyFooter';
export { default as JourneySidebar } from './JourneySidebar';
export { default as JourneyContent } from './JourneyContent';
export { default as MobileTouchHandler } from './MobileTouchHandler';

// Re-export base components
export { 
  BaseStepComponent, 
  StepComponent, 
  StepComponentWrapper, 
  withStepWrapper 
} from '../base/BaseStepComponent';
export { 
  JourneyProvider, 
  useJourneyContext, 
  useJourney 
} from '../base/JourneyContext';

// Export types
export type { 
  StepData, 
  StageData, 
  BaseStepProps, 
  StepComponentProps 
} from '../base/BaseStepComponent';
export type { 
  JourneyContextValue, 
  JourneyProviderProps 
} from '../base/JourneyContext';
export type { 
  JourneyState, 
  JourneyActions, 
  StepStatus, 
  UseJourneyStateReturn 
} from '../../../hooks/useJourneyState';
export type { 
  JourneyProgressData, 
  UseJourneyProgressReturn 
} from '../../../hooks/useJourneyProgress';
