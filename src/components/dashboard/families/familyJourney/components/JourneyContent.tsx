import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useJourneyContext } from '../base/JourneyContext';

export interface JourneyContentProps {
  stageComponentMap: Record<number, React.ComponentType>;
  className?: string;
}

export const JourneyContent: React.FC<JourneyContentProps> = ({ 
  stageComponentMap, 
  className 
}) => {
  const { state, actions, currentStageData, currentStepData } = useJourneyContext();

  // Render the content for the current stage using step components
  const renderStageContent = () => {
    const StageComponent = stageComponentMap[state.currentStage];

    if (StageComponent) {
      return <StageComponent />;
    }

    // Fallback for stages without dedicated components
    return (
      <div className="space-y-4 lg:space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{currentStageData.title}</CardTitle>
            <CardDescription>{currentStageData.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Content for {currentStageData.title} will be implemented here.
            </p>
            <Button 
              className="w-full" 
              onClick={() => actions.markStepComplete(currentStepData.id)}
            >
              Continue
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className={cn("flex-1 overflow-hidden", className)}>
      <ScrollArea className="h-full">
        <div className="p-3 lg:p-4 xl:p-6 pb-16 lg:pb-6">
          {renderStageContent()}
        </div>
      </ScrollArea>
    </div>
  );
};

export default JourneyContent;
