import React from 'react';
import { cn } from '@/lib/utils';
import { useJourneyContext } from '../base/JourneyContext';
import SidebarContent from '../sideBar';
import { stages } from '@/data/familyJourney/familyJourneyStages';

export interface JourneySidebarProps {
  className?: string;
}

export const JourneySidebar: React.FC<JourneySidebarProps> = ({ className }) => {
  const { state, actions } = useJourneyContext();

  return (
    <>
      {/* Enhanced Desktop Sidebar - Right Side */}
      <div className={cn(
        "hidden lg:flex border-l border-border/40 bg-gradient-to-b from-background to-muted/5 flex-shrink-0 transition-all duration-300 ease-in-out",
        "shadow-sm backdrop-blur-sm",
        state.sidebarCollapsed ? "w-20" : "w-80 xl:w-96",
        className
      )}>
        <SidebarContent
          stages={stages}
          currentStage={state.currentStage}
          currentStep={state.currentStep}
          expandedStages={state.expandedStages}
          stepStatus={state.stepStatus}
          toggleStageExpansion={actions.toggleStageExpansion}
          goToStep={actions.goToStep}
          initialCollapsed={state.sidebarCollapsed}
          onCollapsedChange={actions.setSidebarCollapsed}
        />
      </div>

      {/* Mobile Sidebar - Slide-out from right */}
      <div className="lg:hidden">
        {/* Backdrop */}
        {state.mobileSidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black/40"
            onClick={actions.closeMobileSidebar}
            aria-label="Close sidebar backdrop"
          />
        )}

        {/* Sidebar Panel */}
        <div
          className={cn(
            'fixed top-0 right-0 bottom-0 z-50 w-80 bg-background border-l shadow-lg',
            'transition-transform duration-300 ease-in-out',
            state.mobileSidebarOpen ? 'translate-x-0' : 'translate-x-full'
          )}
        >
          <SidebarContent
            stages={stages}
            currentStage={state.currentStage}
            currentStep={state.currentStep}
            expandedStages={state.expandedStages}
            stepStatus={state.stepStatus}
            toggleStageExpansion={actions.toggleStageExpansion}
            goToStep={actions.goToStep}
            isMobileMenuOpen={state.mobileSidebarOpen}
            closeMobileMenu={actions.closeMobileSidebar}
          />
        </div>
      </div>
    </>
  );
};

export default JourneySidebar;
