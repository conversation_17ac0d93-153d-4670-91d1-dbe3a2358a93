import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { useJourneyContext } from '../base/JourneyContext';
import { journeyConfig } from '@/data/familyJourney/familyJourneyStages';

export interface JourneyFooterProps {
  className?: string;
}

export const JourneyFooter: React.FC<JourneyFooterProps> = ({ className }) => {
  const { state, actions, currentStageData, currentStepData } = useJourneyContext();

  const isFirstStep = state.currentStage === 1 && state.currentStep === 0;
  const isStepCompleted = state.stepStatus[currentStepData?.id] || false;

  return (
    <div className={cn("border-t p-3 lg:p-4 xl:p-6 flex-shrink-0 bg-background", className)}>
      <div className="flex items-center justify-between gap-2">
        {journeyConfig.ui.navigation.showPreviousButton && (
          <Button
            variant="outline"
            onClick={actions.prevStep}
            disabled={isFirstStep}
            className="h-8 lg:h-9 px-3 lg:px-4"
            size="sm"
          >
            <ChevronLeft className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
            <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.previousButtonText}</span>
          </Button>
        )}

        {journeyConfig.ui.navigation.showStepCounter && (
          <div className="text-center min-w-0 flex-1 mx-2">
            <div className="text-xs lg:text-sm text-muted-foreground truncate">
              Step {state.currentStep + 1} of {currentStageData.steps.length}
            </div>
            <div className="font-medium text-xs lg:text-sm truncate">{currentStageData.title}</div>
          </div>
        )}

        {journeyConfig.ui.navigation.showNextButton && (
          <Button
            onClick={() => actions.markStepComplete(currentStepData?.id)}
            disabled={isStepCompleted}
            className="h-8 lg:h-9 px-3 lg:px-4"
            size="sm"
          >
            {isStepCompleted ? (
              <>
                <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.doneButtonText}</span>
                <Check className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
              </>
            ) : (
              <>
                <span className="text-xs lg:text-sm">{journeyConfig.ui.navigation.nextButtonText}</span>
                <ChevronRight className="h-3 w-3 lg:h-4 lg:w-4 ml-1 lg:ml-2" />
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default JourneyFooter;
