import {
  User,
  FileCheck,
  Video,
  Users,
  Heart,
  FileText,
  CalendarPlus2Icon as CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
  Clock,
  AlertCircle,
  Settings,
  BookOpen,
  Plane,
  Calendar,
  Phone,
  Mail,
  Upload,
  Download,
  Star,
  Award,
  Target,
  Zap,
} from 'lucide-react';
import stagesData from '../familyJourney/familyJourneyStages.json';
import configData from '../familyJourney/familyJourneyConfig.json';
import { configLoader, validateJourneyConfig, validateStagesConfig } from './configLoader';
import type { Stage as ValidatedStage, JourneyConfig as ValidatedJourneyConfig } from './configLoader';

// Enhanced icon mapping with more icons
const iconMap = {
  User,
  FileCheck,
  Video,
  Users,
  Heart,
  FileText,
  CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
  Clock,
  AlertCircle,
  Settings,
  BookOpen,
  Plane,
  Calendar,
  Phone,
  Mail,
  Upload,
  Download,
  Star,
  Award,
  Target,
  Zap,
};

// Enhanced type definitions with validation
export interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
  defaultStatus: boolean;
  required?: boolean;
  order?: number;
  dependencies?: string[];
  metadata?: Record<string, any>;
}

export interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  componentName: string;
  borderColor: string;
  badgeColor: string;
  steps: Step[];
  enabled?: boolean;
  order?: number;
  category?: string;
  estimatedDuration?: string;
  prerequisites?: number[];
  metadata?: Record<string, any>;
}

// Re-export validated types from configLoader
export type JourneyConfig = ValidatedJourneyConfig;
export type ValidatedStage = ValidatedStage;

// Component mapping types
export interface ComponentMap {
  [key: string]: React.ComponentType<any>;
}

export interface StageComponentMap {
  [stageId: number]: React.ComponentType<any>;
}

export interface ComponentMappingOptions {
  fallbackComponent?: React.ComponentType<any>;
  enableLazyLoading?: boolean;
  validateComponents?: boolean;
  onMissingComponent?: (componentName: string, stageId: number) => void;
}

// Load and validate configuration with error handling
let validatedStages: Stage[] = [];
let validatedConfig: JourneyConfig;

try {
  // Validate and load stages configuration
  const rawValidatedStages = validateStagesConfig(stagesData);

  // Transform the validated data to include actual icon components
  validatedStages = rawValidatedStages.map((stage) => ({
    ...stage,
    icon: iconMap[stage.icon as keyof typeof iconMap] || FileText,
    steps: stage.steps.map((step) => ({
      ...step,
      icon: iconMap[step.icon as keyof typeof iconMap] || FileText,
    })),
  }));

  // Validate and load journey configuration
  validatedConfig = validateJourneyConfig(configData);
} catch (error) {
  console.error('Configuration validation failed:', error);

  // Fallback to basic configuration
  validatedStages = stagesData.map((stage: any) => ({
    ...stage,
    icon: iconMap[stage.icon as keyof typeof iconMap] || FileText,
    steps: stage.steps.map((step: any) => ({
      ...step,
      icon: iconMap[step.icon as keyof typeof iconMap] || FileText,
    })),
  })) as Stage[];

  validatedConfig = configData as JourneyConfig;
}

// Export the validated and transformed data
export const stages: Stage[] = validatedStages;
export const journeyConfig: JourneyConfig = validatedConfig;

// Enhanced helper function to get initial step status from configuration
export const getInitialStepStatus = (): { [key: string]: boolean } => {
  const initialStatus: { [key: string]: boolean } = {};
  stages.forEach(stage => {
    if (stage.enabled !== false) { // Only include enabled stages
      stage.steps.forEach(step => {
        initialStatus[step.id] = step.defaultStatus;
      });
    }
  });
  return initialStatus;
};

// Enhanced stage component mapping with validation and error handling
export const createStageComponentMap = (
  components: ComponentMap,
  options: ComponentMappingOptions = {}
): StageComponentMap => {
  const {
    fallbackComponent,
    validateComponents = true,
    onMissingComponent,
  } = options;

  const componentMap: StageComponentMap = {};

  stages.forEach(stage => {
    if (stage.enabled === false) {
      return; // Skip disabled stages
    }

    const componentName = stage.componentName;
    const component = components[componentName];

    if (component) {
      if (validateComponents) {
        // Basic component validation
        if (typeof component !== 'function') {
          console.warn(`Component ${componentName} for stage ${stage.id} is not a valid React component`);
          return;
        }
      }
      componentMap[stage.id] = component;
    } else {
      // Handle missing component
      if (onMissingComponent) {
        onMissingComponent(componentName, stage.id);
      } else {
        console.warn(`Component ${componentName} not found for stage ${stage.id}`);
      }

      // Use fallback component if provided
      if (fallbackComponent) {
        componentMap[stage.id] = fallbackComponent;
      }
    }
  });

  return componentMap;
};

// Utility functions for configuration management
export const getStageById = (stageId: number): Stage | undefined => {
  return stages.find(stage => stage.id === stageId);
};

export const getStepById = (stepId: string): { stage: Stage; step: Step } | undefined => {
  for (const stage of stages) {
    const step = stage.steps.find(s => s.id === stepId);
    if (step) {
      return { stage, step };
    }
  }
  return undefined;
};

export const getEnabledStages = (): Stage[] => {
  return stages.filter(stage => stage.enabled !== false);
};

export const getStagesByCategory = (category: string): Stage[] => {
  return stages.filter(stage => stage.category === category);
};

export const validateStagePrerequisites = (stageId: number, completedStages: number[]): boolean => {
  const stage = getStageById(stageId);
  if (!stage || !stage.prerequisites) {
    return true;
  }

  return stage.prerequisites.every(prereqId => completedStages.includes(prereqId));
};

export const getNextAvailableStage = (currentStageId: number, completedStages: number[]): Stage | undefined => {
  const enabledStages = getEnabledStages();
  const currentIndex = enabledStages.findIndex(stage => stage.id === currentStageId);

  if (currentIndex === -1 || currentIndex >= enabledStages.length - 1) {
    return undefined;
  }

  // Find next stage that meets prerequisites
  for (let i = currentIndex + 1; i < enabledStages.length; i++) {
    const stage = enabledStages[i];
    if (validateStagePrerequisites(stage.id, completedStages)) {
      return stage;
    }
  }

  return undefined;
};

// Export configuration loader for external use
export { configLoader, validateJourneyConfig, validateStagesConfig };

export default stages;
