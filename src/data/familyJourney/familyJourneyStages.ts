import {
  User,
  <PERSON>Check,
  <PERSON>,
  Users,
  Heart,
  FileText,
  CalendarPlus2Icon as CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
} from 'lucide-react';
import stagesData from '../familyJourney/familyJourneyStages.json';
import configData from '../familyJourney/familyJourneyConfig.json';

// Icon mapping from string names to actual icon components
const iconMap = {
  User,
  FileCheck,
  Video,
  Users,
  Heart,
  FileText,
  CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
};

// Type definitions
export interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
  defaultStatus: boolean;
}

export interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  componentName: string;
  borderColor: string;
  badgeColor: string;
  steps: Step[];
}

export interface JourneyConfig {
  ui: {
    navigation: {
      showPreviousButton: boolean;
      showNextButton: boolean;
      showStepCounter: boolean;
      previousButtonText: string;
      nextButtonText: string;
      doneButtonText: string;
    };
    sidebar: {
      defaultCollapsed: boolean;
      showProgress: boolean;
      showStageProgress: boolean;
      enableMobileSwipe: boolean;
      progressLabels: {
        completed: string;
        currentStage: string;
        journeyProgress: string;
      };
    };
    stages: {
      defaultExpanded: number[];
      allowMultipleExpanded: boolean;
      showCompletionBadges: boolean;
      completionBadgeText: string;
      activeBadgeText: string;
      pendingBadgeText: string;
    };
    steps: {
      autoAdvanceDelay: number;
      showStatusIcons: boolean;
      showDescriptions: boolean;
      completionAnimation: boolean;
    };
  };
  behavior: {
    autoExpandNextStage: boolean;
    autoCollapseCompletedStages: boolean;
    persistProgress: boolean;
    allowSkipSteps: boolean;
    requireSequentialCompletion: boolean;
  };
  styling: {
    theme: {
      primaryColor: string;
      secondaryColor: string;
      successColor: string;
      warningColor: string;
      errorColor: string;
    };
    animations: {
      enableTransitions: boolean;
      transitionDuration: string;
      enableHoverEffects: boolean;
      enableProgressAnimations: boolean;
    };
  };
}

// Transform the JSON data to include actual icon components
export const stages: Stage[] = stagesData.map((stage) => ({
  ...stage,
  icon: iconMap[stage.icon as keyof typeof iconMap] || FileText,
  steps: stage.steps.map((step) => ({
    ...step,
    icon: iconMap[step.icon as keyof typeof iconMap] || FileText,
  })),
}));

// Export the configuration
export const journeyConfig: JourneyConfig = configData;

// Helper function to get initial step status from configuration
export const getInitialStepStatus = (): { [key: string]: boolean } => {
  const initialStatus: { [key: string]: boolean } = {};
  stages.forEach(stage => {
    stage.steps.forEach(step => {
      initialStatus[step.id] = step.defaultStatus;
    });
  });
  return initialStatus;
};

// Helper function to create stage component mapping
export const createStageComponentMap = (components: { [key: string]: React.ComponentType }) => {
  const componentMap: Record<number, React.ComponentType> = {};
  stages.forEach(stage => {
    if (components[stage.componentName]) {
      componentMap[stage.id] = components[stage.componentName];
    }
  });
  return componentMap;
};

export default stages;
