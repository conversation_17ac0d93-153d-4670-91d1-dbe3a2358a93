import { z } from 'zod';

// Zod schemas for validation
const StepSchema = z.object({
  id: z.string().min(1, 'Step ID is required'),
  title: z.string().min(1, 'Step title is required'),
  description: z.string().min(1, 'Step description is required'),
  type: z.enum(['agreement', 'schedule', 'upload', 'form', 'display', 'confirm', 'checklist', 'text']),
  icon: z.string().min(1, 'Step icon is required'),
  defaultStatus: z.boolean(),
  required: z.boolean().optional().default(true),
  order: z.number().optional(),
  dependencies: z.array(z.string()).optional().default([]),
  metadata: z.record(z.any()).optional().default({}),
});

const StageSchema = z.object({
  id: z.number().positive('Stage ID must be positive'),
  title: z.string().min(1, 'Stage title is required'),
  description: z.string().min(1, 'Stage description is required'),
  icon: z.string().min(1, 'Stage icon is required'),
  componentName: z.string().min(1, 'Component name is required'),
  borderColor: z.string().min(1, 'Border color is required'),
  badgeColor: z.string().min(1, 'Badge color is required'),
  steps: z.array(StepSchema).min(1, 'Stage must have at least one step'),
  enabled: z.boolean().optional().default(true),
  order: z.number().optional(),
  category: z.string().optional(),
  estimatedDuration: z.string().optional(),
  prerequisites: z.array(z.number()).optional().default([]),
  metadata: z.record(z.any()).optional().default({}),
});

const NavigationConfigSchema = z.object({
  showPreviousButton: z.boolean(),
  showNextButton: z.boolean(),
  showStepCounter: z.boolean(),
  previousButtonText: z.string(),
  nextButtonText: z.string(),
  doneButtonText: z.string(),
  allowSkipToAnyStep: z.boolean().optional().default(false),
  showProgressBar: z.boolean().optional().default(true),
  confirmBeforeNavigation: z.boolean().optional().default(false),
});

const SidebarConfigSchema = z.object({
  defaultCollapsed: z.boolean(),
  showProgress: z.boolean(),
  showStageProgress: z.boolean(),
  enableMobileSwipe: z.boolean(),
  progressLabels: z.object({
    completed: z.string(),
    currentStage: z.string(),
    journeyProgress: z.string(),
  }),
  showEstimatedTime: z.boolean().optional().default(false),
  enableStageFiltering: z.boolean().optional().default(false),
  showCompletionPercentage: z.boolean().optional().default(true),
});

const StagesConfigSchema = z.object({
  defaultExpanded: z.array(z.number()),
  allowMultipleExpanded: z.boolean(),
  showCompletionBadges: z.boolean(),
  completionBadgeText: z.string(),
  activeBadgeText: z.string(),
  pendingBadgeText: z.string(),
  autoExpandOnProgress: z.boolean().optional().default(true),
  showStageNumbers: z.boolean().optional().default(true),
  enableDragAndDrop: z.boolean().optional().default(false),
});

const StepsConfigSchema = z.object({
  autoAdvanceDelay: z.number().min(0),
  showStatusIcons: z.boolean(),
  showDescriptions: z.boolean(),
  completionAnimation: z.boolean(),
  enableStepValidation: z.boolean().optional().default(true),
  showRequiredIndicator: z.boolean().optional().default(true),
  allowPartialCompletion: z.boolean().optional().default(false),
});

const BehaviorConfigSchema = z.object({
  autoExpandNextStage: z.boolean(),
  autoCollapseCompletedStages: z.boolean(),
  persistProgress: z.boolean(),
  allowSkipSteps: z.boolean(),
  requireSequentialCompletion: z.boolean(),
  enableAutoSave: z.boolean().optional().default(true),
  autoSaveInterval: z.number().optional().default(30000), // 30 seconds
  enableOfflineMode: z.boolean().optional().default(false),
  maxRetryAttempts: z.number().optional().default(3),
});

const ThemeConfigSchema = z.object({
  primaryColor: z.string(),
  secondaryColor: z.string(),
  successColor: z.string(),
  warningColor: z.string(),
  errorColor: z.string(),
  backgroundColor: z.string().optional().default('background'),
  textColor: z.string().optional().default('foreground'),
  borderRadius: z.string().optional().default('md'),
});

const AnimationsConfigSchema = z.object({
  enableTransitions: z.boolean(),
  transitionDuration: z.string(),
  enableHoverEffects: z.boolean(),
  enableProgressAnimations: z.boolean(),
  enableLoadingStates: z.boolean().optional().default(true),
  enableMicroInteractions: z.boolean().optional().default(true),
});

const StylingConfigSchema = z.object({
  theme: ThemeConfigSchema,
  animations: AnimationsConfigSchema,
  customCSS: z.string().optional(),
  responsiveBreakpoints: z.record(z.string()).optional(),
});

const UIConfigSchema = z.object({
  navigation: NavigationConfigSchema,
  sidebar: SidebarConfigSchema,
  stages: StagesConfigSchema,
  steps: StepsConfigSchema,
});

const JourneyConfigSchema = z.object({
  ui: UIConfigSchema,
  behavior: BehaviorConfigSchema,
  styling: StylingConfigSchema,
  version: z.string().optional().default('1.0.0'),
  lastUpdated: z.string().optional(),
  environment: z.enum(['development', 'staging', 'production']).optional().default('development'),
  features: z.record(z.boolean()).optional().default({}),
});

const StagesArraySchema = z.array(StageSchema);

// Type exports
export type Step = z.infer<typeof StepSchema>;
export type Stage = z.infer<typeof StageSchema>;
export type JourneyConfig = z.infer<typeof JourneyConfigSchema>;
export type NavigationConfig = z.infer<typeof NavigationConfigSchema>;
export type SidebarConfig = z.infer<typeof SidebarConfigSchema>;
export type StagesConfig = z.infer<typeof StagesConfigSchema>;
export type StepsConfig = z.infer<typeof StepsConfigSchema>;
export type BehaviorConfig = z.infer<typeof BehaviorConfigSchema>;
export type StylingConfig = z.infer<typeof StylingConfigSchema>;
export type ThemeConfig = z.infer<typeof ThemeConfigSchema>;
export type AnimationsConfig = z.infer<typeof AnimationsConfigSchema>;

// Configuration loader with validation
export class ConfigLoader {
  private static instance: ConfigLoader;
  private cachedConfig: JourneyConfig | null = null;
  private cachedStages: Stage[] | null = null;

  private constructor() {}

  static getInstance(): ConfigLoader {
    if (!ConfigLoader.instance) {
      ConfigLoader.instance = new ConfigLoader();
    }
    return ConfigLoader.instance;
  }

  // Load and validate journey configuration
  loadJourneyConfig(configData: unknown): JourneyConfig {
    try {
      const validatedConfig = JourneyConfigSchema.parse(configData);
      this.cachedConfig = validatedConfig;
      return validatedConfig;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new Error(`Journey configuration validation failed: ${errorMessages}`);
      }
      throw new Error(`Failed to load journey configuration: ${error}`);
    }
  }

  // Load and validate stages configuration
  loadStagesConfig(stagesData: unknown): Stage[] {
    try {
      const validatedStages = StagesArraySchema.parse(stagesData);
      
      // Additional validation: check for duplicate IDs
      const stageIds = validatedStages.map(stage => stage.id);
      const duplicateIds = stageIds.filter((id, index) => stageIds.indexOf(id) !== index);
      if (duplicateIds.length > 0) {
        throw new Error(`Duplicate stage IDs found: ${duplicateIds.join(', ')}`);
      }

      // Validate step IDs within each stage
      validatedStages.forEach(stage => {
        const stepIds = stage.steps.map(step => step.id);
        const duplicateStepIds = stepIds.filter((id, index) => stepIds.indexOf(id) !== index);
        if (duplicateStepIds.length > 0) {
          throw new Error(`Duplicate step IDs found in stage ${stage.id}: ${duplicateStepIds.join(', ')}`);
        }
      });

      this.cachedStages = validatedStages;
      return validatedStages;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new Error(`Stages configuration validation failed: ${errorMessages}`);
      }
      throw new Error(`Failed to load stages configuration: ${error}`);
    }
  }

  // Get cached configuration
  getCachedConfig(): JourneyConfig | null {
    return this.cachedConfig;
  }

  // Get cached stages
  getCachedStages(): Stage[] | null {
    return this.cachedStages;
  }

  // Validate a single step
  validateStep(stepData: unknown): Step {
    return StepSchema.parse(stepData);
  }

  // Validate a single stage
  validateStage(stageData: unknown): Stage {
    return StageSchema.parse(stageData);
  }

  // Clear cache
  clearCache(): void {
    this.cachedConfig = null;
    this.cachedStages = null;
  }

  // Get schema for external validation
  static getSchemas() {
    return {
      StepSchema,
      StageSchema,
      JourneyConfigSchema,
      StagesArraySchema,
    };
  }
}

// Export singleton instance
export const configLoader = ConfigLoader.getInstance();

// Utility functions
export const validateJourneyConfig = (data: unknown): JourneyConfig => {
  return configLoader.loadJourneyConfig(data);
};

export const validateStagesConfig = (data: unknown): Stage[] => {
  return configLoader.loadStagesConfig(data);
};

export default configLoader;
