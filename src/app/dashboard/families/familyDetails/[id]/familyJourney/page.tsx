import { use<PERSON>arams } from 'next/navigation';

// Import new components
import { JourneyProvider } from '../../../../../../components/dashboard/families/familyJourney/base/JourneyContext';
import JourneyHeader from '../../../../../../components/dashboard/families/familyJourney/components/JourneyHeader';
import JourneyFooter from '../../../../../../components/dashboard/families/familyJourney/components/JourneyFooter';
import JourneySidebar from '../../../../../../components/dashboard/families/familyJourney/components/JourneySidebar';
import JourneyContent from '../../../../../../components/dashboard/families/familyJourney/components/JourneyContent';
import MobileTouchHandler from '../../../../../../components/dashboard/families/familyJourney/components/MobileTouchHandler';

// Import step components
import IntakeStep from '../../../../../../components/dashboard/families/familyJourney/intakeStep.tsx';
import ProfileStep from '../../../../../../components/dashboard/families/familyJourney/profileStep.tsx';
import ComplianceStep from '../../../../../../components/dashboard/families/familyJourney/complianceStep.tsx';
import MatchingStep from '../../../../../../components/dashboard/families/familyJourney/matchingStep.tsx';
import TravelStep from '../../../../../../components/dashboard/families/familyJourney/travelStep.tsx';
import PreArrivalStep from '../../../../../../components/dashboard/families/familyJourney/preArrivalStep.tsx';
import ArrivalStep from '../../../../../../components/dashboard/families/familyJourney/arrivalStep.tsx';
import ProgramStep from '../../../../../../components/dashboard/families/familyJourney/programStep.tsx';
import DepartureStep from '../../../../../../components/dashboard/families/familyJourney/departureStep.tsx';

// Import stages data and configuration
import { createStageComponentMap } from '@/data/familyJourney/familyJourneyStages.ts';


// Create enhanced stage component mapping with validation and error handling
const stageComponentMap = createStageComponentMap({
  IntakeStep,
  ProfileStep,
  ComplianceStep,
  MatchingStep,
  TravelStep,
  PreArrivalStep,
  ArrivalStep,
  ProgramStep,
  DepartureStep,
}, {
  validateComponents: true,
  onMissingComponent: (componentName, stageId) => {
    console.warn(`Missing component ${componentName} for stage ${stageId}. Using fallback.`);
  },
});

export default function ModernAupairForm() {
  const params = useParams();
  const familyId = params?.id as string;

  return (
    <JourneyProvider familyId={familyId} autoLoadProgress autoSaveProgress>
      <MobileTouchHandler>
        <div className="h-screen flex flex-col bg-background">
          <div className="flex flex-1 overflow-hidden">
            {/* Main Content */}
            <div className="flex-1 flex flex-col min-w-0 overflow-hidden transition-all duration-300">
              <JourneyHeader />
              <JourneyContent stageComponentMap={stageComponentMap} />
              <JourneyFooter />
            </div>
            <JourneySidebar />
          </div>
        </div>
      </MobileTouchHandler>
    </JourneyProvider>
  );
}
