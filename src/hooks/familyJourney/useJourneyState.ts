import { useState, useCallback } from 'react';
import { stages, journeyConfig, getInitialStepStatus } from '@/data/familyJourney/familyJourneyStages';

export interface StepStatus {
  [key: string]: boolean;
}

export interface JourneyState {
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  sidebarCollapsed: boolean;
  mobileSidebarOpen: boolean;
  stepStatus: StepStatus;
}

export interface JourneyActions {
  setCurrentStage: (stage: number) => void;
  setCurrentStep: (step: number) => void;
  setExpandedStages: (stages: number[]) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setMobileSidebarOpen: (open: boolean) => void;
  setStepStatus: (status: StepStatus | ((prev: StepStatus) => StepStatus)) => void;
  nextStep: () => void;
  prevStep: () => void;
  markStepComplete: (stepId: string) => void;
  toggleStageExpansion: (stageId: number) => void;
  goToStep: (stageId: number, stepIndex: number) => void;
  openMobileSidebar: () => void;
  closeMobileSidebar: () => void;
}

export interface UseJourneyStateReturn {
  state: JourneyState;
  actions: JourneyActions;
  currentStageData: any;
  currentStepData: any;
}

export const useJourneyState = (): UseJourneyStateReturn => {
  // Initialize state
  const [currentStage, setCurrentStage] = useState(1);
  const [currentStep, setCurrentStep] = useState(0);
  const [expandedStages, setExpandedStages] = useState(journeyConfig.ui.stages.defaultExpanded);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(journeyConfig.ui.sidebar.defaultCollapsed);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const [stepStatus, setStepStatus] = useState<StepStatus>(getInitialStepStatus());

  // Derived state
  const currentStageData = stages.find(stage => stage.id === currentStage)!;
  const currentStepData = currentStageData.steps[currentStep];

  // Navigation actions
  const nextStep = useCallback(() => {
    if (currentStep < currentStageData.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else if (currentStage < stages.length) {
      setCurrentStage(currentStage + 1);
      setCurrentStep(0);
      // Expand the next stage if configured to do so
      if (journeyConfig.behavior.autoExpandNextStage && !expandedStages.includes(currentStage + 1)) {
        setExpandedStages([...expandedStages, currentStage + 1]);
      }
    }
  }, [currentStep, currentStage, currentStageData.steps.length, expandedStages]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else if (currentStage > 1) {
      setCurrentStage(currentStage - 1);
      setCurrentStep(stages[currentStage - 2].steps.length - 1);
      // Expand the previous stage if configured to do so
      if (journeyConfig.behavior.autoExpandNextStage && !expandedStages.includes(currentStage - 1)) {
        setExpandedStages([...expandedStages, currentStage - 1]);
      }
    }
  }, [currentStep, currentStage, expandedStages]);

  // Handler to mark a step as complete and move to next step
  const markStepComplete = useCallback((stepId: string) => {
    setStepStatus(prev => ({ ...prev, [stepId]: true }));
    setTimeout(nextStep, journeyConfig.ui.steps.autoAdvanceDelay);
  }, [nextStep]);

  // Handler to expand/collapse a stage
  const toggleStageExpansion = useCallback((stageId: number) => {
    if (expandedStages.includes(stageId)) {
      setExpandedStages(expandedStages.filter(id => id !== stageId));
    } else {
      setExpandedStages([...expandedStages, stageId]);
    }
  }, [expandedStages]);

  // Handler to go to a specific step in a stage
  const goToStep = useCallback((stageId: number, stepIndex: number) => {
    setCurrentStage(stageId);
    setCurrentStep(stepIndex);
    setMobileSidebarOpen(false); // Close mobile sidebar when navigating
  }, []);

  // Mobile sidebar handlers
  const openMobileSidebar = useCallback(() => setMobileSidebarOpen(true), []);
  const closeMobileSidebar = useCallback(() => setMobileSidebarOpen(false), []);

  const state: JourneyState = {
    currentStage,
    currentStep,
    expandedStages,
    sidebarCollapsed,
    mobileSidebarOpen,
    stepStatus,
  };

  const actions: JourneyActions = {
    setCurrentStage,
    setCurrentStep,
    setExpandedStages,
    setSidebarCollapsed,
    setMobileSidebarOpen,
    setStepStatus,
    nextStep,
    prevStep,
    markStepComplete,
    toggleStageExpansion,
    goToStep,
    openMobileSidebar,
    closeMobileSidebar,
  };

  return {
    state,
    actions,
    currentStageData,
    currentStepData,
  };
};
