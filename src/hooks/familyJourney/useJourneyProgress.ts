import { useEffect, useCallback } from 'react';
import { journeyConfig } from '@/data/familyJourney/familyJourneyStages';
import { type JourneyState, type StepStatus } from './useJourneyState';

export interface JourneyProgressData {
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  stepStatus: StepStatus;
  lastUpdated: string;
  familyId?: string;
}

export interface UseJourneyProgressReturn {
  saveProgress: (state: JourneyState, familyId?: string) => void;
  loadProgress: (familyId?: string) => JourneyProgressData | null;
  clearProgress: (familyId?: string) => void;
  getProgressKey: (familyId?: string) => string;
}

export const useJourneyProgress = (): UseJourneyProgressReturn => {
  // Generate storage key based on family ID
  const getProgressKey = useCallback((familyId?: string): string => {
    const baseKey = 'family-journey-progress';
    return familyId ? `${baseKey}-${familyId}` : baseKey;
  }, []);

  // Save progress to localStorage
  const saveProgress = useCallback((state: JourneyState, familyId?: string) => {
    if (!journeyConfig.behavior.persistProgress) {
      return;
    }

    try {
      const progressData: JourneyProgressData = {
        currentStage: state.currentStage,
        currentStep: state.currentStep,
        expandedStages: state.expandedStages,
        stepStatus: state.stepStatus,
        lastUpdated: new Date().toISOString(),
        familyId,
      };

      const key = getProgressKey(familyId);
      localStorage.setItem(key, JSON.stringify(progressData));
    } catch (error) {
      console.warn('Failed to save journey progress:', error);
    }
  }, [getProgressKey]);

  // Load progress from localStorage
  const loadProgress = useCallback((familyId?: string): JourneyProgressData | null => {
    if (!journeyConfig.behavior.persistProgress) {
      return null;
    }

    try {
      const key = getProgressKey(familyId);
      const stored = localStorage.getItem(key);
      
      if (!stored) {
        return null;
      }

      const progressData: JourneyProgressData = JSON.parse(stored);
      
      // Validate the data structure
      if (
        typeof progressData.currentStage !== 'number' ||
        typeof progressData.currentStep !== 'number' ||
        !Array.isArray(progressData.expandedStages) ||
        typeof progressData.stepStatus !== 'object'
      ) {
        console.warn('Invalid journey progress data structure, clearing storage');
        clearProgress(familyId);
        return null;
      }

      return progressData;
    } catch (error) {
      console.warn('Failed to load journey progress:', error);
      return null;
    }
  }, [clearProgress, getProgressKey]);

  // Clear progress from localStorage
  const clearProgress = useCallback((familyId?: string) => {
    try {
      const key = getProgressKey(familyId);
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('Failed to clear journey progress:', error);
    }
  }, [getProgressKey]);

  return {
    saveProgress,
    loadProgress,
    clearProgress,
    getProgressKey,
  };
};

// Hook to automatically persist journey state
export const useJourneyPersistence = (
  state: JourneyState,
  familyId?: string
): UseJourneyProgressReturn => {
  const progressHook = useJourneyProgress();

  // Auto-save when state changes
  useEffect(() => {
    if (!journeyConfig.behavior.persistProgress) {
      return;
    }

    const timeoutId = setTimeout(() => {
      progressHook.saveProgress(state, familyId);
    }, 1000); // Debounce saves

    return () => clearTimeout(timeoutId);
  }, [state.currentStage, state.currentStep, state.expandedStages, state.stepStatus, familyId, progressHook, state]);

  return progressHook;
};

// Utility function to restore state from saved progress
export const restoreJourneyState = (
  progressData: JourneyProgressData): Partial<JourneyState> => {
  return {
    currentStage: progressData.currentStage,
    currentStep: progressData.currentStep,
    expandedStages: progressData.expandedStages,
    stepStatus: progressData.stepStatus,
  };
};
